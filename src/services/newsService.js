/**
 * 新闻API服务 - 简化版
 * 获取AI相关资讯
 */

// 支持的平台配置
const PLATFORMS = {
    '36kr': { name: '36氪' },
    juejin: { name: '掘金' },
    zhihu: { name: '知乎' },
    github: { name: 'GitH<PERSON>' }
};

// AI相关关键词（精简版）
const AI_KEYWORDS = [
    'AI',
    '人工智能',
    '机器学习',
    'ChatGPT',
    'GPT',
    'LLM',
    '大模型',
    'OpenAI',
    'Claude',
    'Gemini',
    'AIGC',
    '自动化',
    '智能化'
];

// 缓存配置
const CACHE_KEY = 'ai_news_cache';
const CACHE_DURATION = 30 * 60 * 1000; // 30分钟缓存

class NewsService {
    constructor() {
        this.baseUrl = 'https://orz.ai/api/v1/dailynews';
    }

    /**
     * 获取指定平台的新闻数据
     * @param {string} platform - 平台标识
     * @returns {Promise<Array>} 新闻文章数组
     */
    async fetchPlatformNews(platform) {
        try {
            const response = await fetch(`${this.baseUrl}/?platform=${platform}`, {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' },
                signal: AbortSignal.timeout(10000)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();

            if (data.status === '200' && data.data) {
                return this.transformNewsData(data.data, platform);
            }
            return [];
        } catch (error) {
            console.error(`获取 ${platform} 新闻失败:`, error);
            return [];
        }
    }

    /**
     * 转换新闻数据格式
     * @param {Array} rawData - 原始数据
     * @param {string} platform - 平台标识
     * @returns {Array} 格式化的新闻数据
     */
    transformNewsData(rawData, platform) {
        const platformConfig = PLATFORMS[platform];

        return rawData.map((item, index) => ({
            id: `${platform}_${Date.now()}_${index}`,
            title: item.title || '无标题',
            url: item.url || '#',
            date: new Date(),
            source: platformConfig?.name || platform
        }));
    }

    /**
     * AI内容过滤
     * @param {Array} articles - 文章数组
     * @returns {Array} 过滤后的AI相关文章
     */
    filterAIContent(articles) {
        return articles.filter(article => {
            const titleLower = article.title.toLowerCase();
            return AI_KEYWORDS.some(keyword => titleLower.includes(keyword.toLowerCase()));
        });
    }

    /**
     * 简单缓存管理
     */
    getCache() {
        try {
            const cached = localStorage.getItem(CACHE_KEY);
            if (cached) {
                const data = JSON.parse(cached);
                if (Date.now() - data.timestamp < CACHE_DURATION) {
                    return data.articles;
                }
                localStorage.removeItem(CACHE_KEY);
            }
        } catch (error) {
            console.error('缓存读取失败:', error);
        }
        return null;
    }

    setCache(articles) {
        try {
            const data = {
                articles,
                timestamp: Date.now()
            };
            localStorage.setItem(CACHE_KEY, JSON.stringify(data));
        } catch (error) {
            console.error('缓存存储失败:', error);
        }
    }

    /**
     * 获取AI新闻
     * @returns {Promise<Array>} AI新闻文章数组
     */
    async getAINews() {
        // 先尝试从缓存获取
        const cached = this.getCache();
        if (cached) {
            console.log('从缓存获取AI新闻数据');
            return cached;
        }

        console.log('开始获取最新AI新闻数据...');

        try {
            // 并发获取多个平台的数据
            const platforms = Object.keys(PLATFORMS);
            const promises = platforms.map(platform => this.fetchPlatformNews(platform));
            const results = await Promise.allSettled(promises);

            // 合并所有成功的结果
            let allArticles = [];
            results.forEach(result => {
                if (result.status === 'fulfilled' && result.value) {
                    allArticles = allArticles.concat(result.value);
                }
            });

            // 过滤AI相关内容
            const aiArticles = this.filterAIContent(allArticles);

            // 简单去重（基于标题前20个字符）
            const uniqueArticles = this.removeDuplicates(aiArticles);

            // 按时间排序，限制数量
            const finalArticles = uniqueArticles.sort((a, b) => b.date - a.date).slice(0, 30);

            // 存储到缓存
            this.setCache(finalArticles);

            console.log(`成功获取 ${finalArticles.length} 条AI新闻`);
            return finalArticles;
        } catch (error) {
            console.error('获取AI新闻失败:', error);
            return [];
        }
    }

    /**
     * 去除重复文章
     * @param {Array} articles - 文章数组
     * @returns {Array} 去重后的文章数组
     */
    removeDuplicates(articles) {
        const seen = new Set();
        return articles.filter(article => {
            const key = article.title.substring(0, 20);
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }

    /**
     * 清空缓存
     */
    clearCache() {
        try {
            localStorage.removeItem(CACHE_KEY);
            console.log('新闻缓存已清空');
        } catch (error) {
            console.error('清空缓存失败:', error);
        }
    }
}

// 创建单例实例
const newsService = new NewsService();
export default newsService;
