/**
 * 新闻API服务
 * 集成orz.ai接口获取科技资讯，实现缓存和AI内容过滤
 */

// 支持的平台配置
const PLATFORMS = {
    '36kr': { id: '36kr', name: '36氪', category: 'AI工具' },
    juejin: { id: 'juejin', name: '掘金', category: 'AI模型' },
    zhihu: { id: 'zhihu', name: '知乎热榜', category: '行业动态' },
    github: { id: 'github', name: 'GitHub Trending', category: '技术前沿' },
    hackernews: { id: 'hackernews', name: 'Hacker News', category: '技术前沿' }
};

// AI相关关键词列表
const AI_KEYWORDS = [
    'AI',
    '人工智能',
    '机器学习',
    'ML',
    '深度学习',
    'DL',
    'ChatGPT',
    'GPT',
    'LLM',
    '大模型',
    '智能',
    'OpenAI',
    'DeepSeek',
    '豆包',
    '文心',
    'Qwen',
    'Claude',
    'Gemini',
    'Midjourney',
    'Stable Diffusion',
    'AIGC',
    '生成式AI',
    '自动驾驶',
    '计算机视觉',
    'NLP',
    '自然语言处理',
    'RPA',
    '机器人',
    'robot',
    'TensorFlow',
    'PyTorch',
    '神经网络',
    '算法',
    'algorithm',
    'automation',
    '自动化',
    '智能化',
    '数字化',
    'AGI'
];

// 缓存键前缀
const CACHE_PREFIX = 'ai_news_';
const CACHE_DURATION = 60 * 60 * 1000; // 1小时缓存时间

class NewsService {
    constructor() {
        this.baseUrl = 'https://orz.ai/api/v1/dailynews';
        this.updateTimer = null;
        this.isUpdating = false;
        this.initUpdateTimer();
    }

    /**
     * 从orz.ai获取指定平台的新闻数据
     * @param {string} platform - 平台标识
     * @returns {Promise<Array>} 新闻文章数组
     */
    async fetchPlatformNews(platform) {
        try {
            const response = await fetch(`${this.baseUrl}/?platform=${platform}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                },
                // 超时设置
                signal: AbortSignal.timeout(10000)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.status === '200' && data.data) {
                return this.transformNewsData(data.data, platform);
            } else {
                console.warn(`平台 ${platform} 返回数据格式异常:`, data);
                return [];
            }
        } catch (error) {
            console.error(`获取 ${platform} 新闻失败:`, error);
            return [];
        }
    }

    /**
     * 转换新闻数据格式
     * @param {Array} rawData - 原始数据
     * @param {string} platform - 平台标识
     * @returns {Array} 格式化的新闻数据
     */
    transformNewsData(rawData, platform) {
        const platformConfig = PLATFORMS[platform];

        return rawData.map((item, index) => ({
            id: `${platform}_${Date.now()}_${index}`,
            title: item.title || '无标题',
            excerpt: this.generateExcerpt(item.title, item.desc),
            url: item.url || '#',
            image: this.getDefaultImage(),
            category: platformConfig?.category || '科技资讯',
            categoryId: platform,
            date: new Date(),
            tags: this.extractTags(item.title),
            readCount: this.generateReadCount(),
            score: item.score || 0,
            featured: this.shouldBeFeatured(item.title, item.score),
            source: platformConfig?.name || platform
        }));
    }

    /**
     * 生成文章摘要
     * @param {string} title - 标题
     * @param {string} desc - 描述
     * @returns {string} 摘要
     */
    generateExcerpt(title, desc) {
        if (desc && desc.length > 10) {
            return desc.length > 100 ? desc.substring(0, 100) + '...' : desc;
        }

        // 如果没有描述，基于标题生成简单摘要
        const excerpts = [
            '了解最新的技术发展动态和行业趋势。',
            '深入探讨当前热门的技术话题和创新应用。',
            '分析行业最新动向，把握技术发展脉搏。',
            '探索前沿科技，发现创新机遇。'
        ];
        return excerpts[Math.floor(Math.random() * excerpts.length)];
    }

    /**
     * 获取默认图片
     * @returns {string} 图片URL
     */
    getDefaultImage() {
        const images = [
            'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=400&h=250&fit=crop&crop=smart',
            'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=250&fit=crop&crop=smart',
            'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=250&fit=crop&crop=smart',
            'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=250&fit=crop&crop=smart'
        ];
        return images[Math.floor(Math.random() * images.length)];
    }

    /**
     * 从标题中提取标签
     * @param {string} title - 标题
     * @returns {Array} 标签数组
     */
    extractTags(title) {
        const tags = [];
        const titleLower = title.toLowerCase();

        // 检查AI相关关键词
        AI_KEYWORDS.forEach(keyword => {
            if (titleLower.includes(keyword.toLowerCase())) {
                tags.push(keyword);
            }
        });

        // 添加一些通用标签
        if (tags.length === 0) {
            tags.push('科技', '资讯');
        }

        return tags.slice(0, 3); // 最多3个标签
    }

    /**
     * 生成随机阅读量
     * @returns {number} 阅读量
     */
    generateReadCount() {
        return Math.floor(Math.random() * 2000) + 100;
    }

    /**
     * 判断是否应该设为精选
     * @param {string} title - 标题
     * @param {string|number} score - 热度分数
     * @returns {boolean} 是否精选
     */
    shouldBeFeatured(title, score) {
        // 基于关键词和热度判断
        const titleLower = title.toLowerCase();
        const hasHighValueKeywords = [
            '突破',
            '发布',
            '开源',
            'openai',
            'chatgpt',
            '百亿',
            '千亿',
            '重磅'
        ].some(keyword => titleLower.includes(keyword));

        const numScore = parseInt(score) || 0;
        return hasHighValueKeywords || numScore > 1000000 || Math.random() < 0.2;
    }

    /**
     * AI内容过滤
     * @param {Array} articles - 文章数组
     * @returns {Array} 过滤后的AI相关文章
     */
    filterAIContent(articles) {
        return articles.filter(article => {
            const titleLower = article.title.toLowerCase();
            const excerptLower = article.excerpt.toLowerCase();

            return AI_KEYWORDS.some(
                keyword =>
                    titleLower.includes(keyword.toLowerCase()) ||
                    excerptLower.includes(keyword.toLowerCase())
            );
        });
    }

    /**
     * 从缓存获取数据
     * @param {string} key - 缓存键
     * @returns {Object|null} 缓存的数据
     */
    getFromCache(key) {
        try {
            const cached = localStorage.getItem(CACHE_PREFIX + key);
            if (cached) {
                const data = JSON.parse(cached);
                if (Date.now() - data.timestamp < CACHE_DURATION) {
                    return data.value;
                } else {
                    localStorage.removeItem(CACHE_PREFIX + key);
                }
            }
        } catch (error) {
            console.error('缓存读取失败:', error);
        }
        return null;
    }

    /**
     * 存储到缓存
     * @param {string} key - 缓存键
     * @param {any} value - 要缓存的值
     */
    setToCache(key, value) {
        try {
            const data = {
                value,
                timestamp: Date.now()
            };
            localStorage.setItem(CACHE_PREFIX + key, JSON.stringify(data));
        } catch (error) {
            console.error('缓存存储失败:', error);
        }
    }

    /**
     * 获取所有AI新闻
     * @param {boolean} forceRefresh - 是否强制刷新
     * @returns {Promise<Array>} AI新闻文章数组
     */
    async getAINews(forceRefresh = false) {
        const cacheKey = 'all_ai_news';

        // 如果不强制刷新，先尝试从缓存获取
        if (!forceRefresh) {
            const cached = this.getFromCache(cacheKey);
            if (cached && cached.length > 0) {
                console.log('从缓存获取AI新闻数据');
                return cached;
            }
        }

        console.log('开始获取最新AI新闻数据...');

        try {
            // 并发获取多个平台的数据
            const platforms = Object.keys(PLATFORMS);
            const promises = platforms.map(platform => this.fetchPlatformNews(platform));

            const results = await Promise.allSettled(promises);

            // 合并所有成功的结果
            let allArticles = [];
            results.forEach((result, index) => {
                if (result.status === 'fulfilled' && result.value) {
                    allArticles = allArticles.concat(result.value);
                } else {
                    console.warn(`平台 ${platforms[index]} 数据获取失败:`, result.reason);
                }
            });

            // 过滤AI相关内容
            const aiArticles = this.filterAIContent(allArticles);

            // 去重（基于标题相似度）
            const uniqueArticles = this.removeDuplicates(aiArticles);

            // 按时间和热度排序
            uniqueArticles.sort((a, b) => {
                if (a.featured && !b.featured) {
                    return -1;
                }
                if (!a.featured && b.featured) {
                    return 1;
                }
                return b.date - a.date;
            });

            // 限制数量并存储到缓存
            const finalArticles = uniqueArticles.slice(0, 50);
            this.setToCache(cacheKey, finalArticles);

            console.log(`成功获取 ${finalArticles.length} 条AI新闻`);
            return finalArticles;
        } catch (error) {
            console.error('获取AI新闻失败:', error);
            return [];
        }
    }

    /**
     * 去除重复文章
     * @param {Array} articles - 文章数组
     * @returns {Array} 去重后的文章数组
     */
    removeDuplicates(articles) {
        const seen = new Set();
        return articles.filter(article => {
            // 基于标题的前20个字符判断重复
            const key = article.title.substring(0, 20);
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }

    /**
     * 初始化定时更新器
     */
    initUpdateTimer() {
        // 每小时更新一次
        this.updateTimer = setInterval(async () => {
            if (!this.isUpdating) {
                console.log('定时更新AI新闻开始...');
                this.isUpdating = true;
                try {
                    await this.getAINews(true); // 强制刷新
                    console.log('定时更新AI新闻完成');
                } catch (error) {
                    console.error('定时更新失败:', error);
                } finally {
                    this.isUpdating = false;
                }
            }
        }, 60 * 60 * 1000); // 1小时 = 60 * 60 * 1000 毫秒
    }

    /**
     * 手动刷新新闻
     * @returns {Promise<Array>} 最新的新闻数据
     */
    async refreshNews() {
        return await this.getAINews(true);
    }

    /**
     * 销毁服务，清理定时器
     */
    destroy() {
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
            this.updateTimer = null;
        }
    }

    /**
     * 清空缓存
     */
    clearCache() {
        try {
            Object.keys(localStorage).forEach(key => {
                if (key.startsWith(CACHE_PREFIX)) {
                    localStorage.removeItem(key);
                }
            });
            console.log('新闻缓存已清空');
        } catch (error) {
            console.error('清空缓存失败:', error);
        }
    }
}

// 创建单例实例
export const newsService = new NewsService();

// 默认导出
export default newsService;
