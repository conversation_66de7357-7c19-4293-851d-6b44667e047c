<template>
    <div class="news-page">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
            <div class="loading-spinner"></div>
            <p class="loading-text">正在获取最新AI资讯...</p>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error && articles.length === 0" class="error-container">
            <div class="error-icon">⚠️</div>
            <h3 class="error-title">获取资讯失败</h3>
            <p class="error-message">{{ error }}</p>
            <button class="retry-btn" @click="loadNews">重试</button>
        </div>

        <!-- 正常内容 -->
        <div v-else>
            <!-- 顶部操作栏 -->
            <div class="news-header">
                <span class="news-count">{{ articles.length }} 篇AI资讯</span>
                <button class="refresh-btn" @click="loadNews" :disabled="loading">
                    <span class="refresh-icon">🔄</span>
                    {{ loading ? '更新中...' : '刷新' }}
                </button>
            </div>

            <!-- 资讯列表 -->
            <div class="news-list">
                <div
                    v-for="article in articles"
                    :key="article.id"
                    class="news-card"
                    @click="openArticle(article.url)"
                >
                    <div class="article-content">
                        <div class="article-meta">
                            <span class="article-source">{{ article.source }}</span>
                            <span class="article-date">{{ formatTime(article.date) }}</span>
                        </div>
                        <h3 class="article-title">{{ article.title }}</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import newsService from '@/services/newsService';

// 响应式状态
const articles = ref([]);
const loading = ref(false);
const error = ref('');

/**
 * 加载新闻数据
 */
const loadNews = async () => {
    try {
        loading.value = true;
        error.value = '';

        const newsData = await newsService.getAINews();
        articles.value = newsData || [];

        if (articles.value.length === 0) {
            error.value = '暂无AI相关资讯，请稍后再试';
        }
    } catch (err) {
        console.error('加载新闻失败:', err);
        error.value = err.message || '网络连接失败，请检查网络设置';
    } finally {
        loading.value = false;
    }
};

/**
 * 格式化时间
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的时间字符串
 */
const formatTime = date => {
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
        return '昨天';
    }
    if (diffDays <= 7) {
        return `${diffDays}天前`;
    }

    return date.toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric'
    });
};

/**
 * 打开文章链接
 * @param {string} url - 文章URL
 */
const openArticle = url => {
    if (url && url !== '#') {
        window.open(url, '_blank', 'noopener,noreferrer');
    }
};

// 生命周期钩子
onMounted(() => {
    loadNews();
});
</script>

<style lang="scss" scoped>
.news-page {
    padding: 12px;
    height: 100%;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    color: #666;

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #e5e5ea;
        border-top: 3px solid #007aff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 16px;
    }

    .loading-text {
        font-size: 14px;
        margin: 0;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    text-align: center;
    color: #666;

    .error-icon {
        font-size: 48px;
        margin-bottom: 16px;
    }

    .error-title {
        font-size: 18px;
        font-weight: 600;
        color: #000;
        margin: 0 0 8px 0;
    }

    .error-message {
        font-size: 14px;
        margin: 0 0 20px 0;
        max-width: 300px;
    }

    .retry-btn {
        background: #007aff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 18px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
            background: #0056d0;
        }
    }
}

.news-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    padding: 8px 0;

    .news-count {
        font-size: 16px;
        font-weight: 600;
        color: #000;
    }

    .refresh-btn {
        display: flex;
        align-items: center;
        gap: 6px;
        background: #007aff;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 16px;
        font-size: 13px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover:not(:disabled) {
            background: #0056d0;
        }

        &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
    }
}

.news-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.news-card {
    background: #f2f2f7;
    border-radius: 10px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #e5e5e7;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border-color: #007aff;

        .article-title {
            color: #007aff;
        }
    }

    .article-content {
        .article-meta {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;

            .article-source {
                background: #34c759;
                color: white;
                padding: 2px 8px;
                border-radius: 4px;
                font-size: 11px;
                font-weight: 600;
            }

            .article-date {
                color: #999;
                font-size: 12px;
            }
        }

        .article-title {
            font-size: 16px;
            font-weight: 600;
            color: #000;
            line-height: 1.4;
            transition: color 0.2s ease;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    }
}

@media (max-width: 480px) {
    .news-page {
        padding: 8px;
    }

    .news-header .news-count {
        font-size: 14px;
    }

    .news-card {
        padding: 12px;

        .article-content .article-title {
            font-size: 14px;
        }
    }
}
</style>
