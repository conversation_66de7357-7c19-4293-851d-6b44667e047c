<template>
    <div class="news-page">
        <!-- 加载状态 -->
        <div v-if="isInitialLoading" class="loading-container">
            <div class="loading-spinner"></div>
            <p class="loading-text">正在获取最新AI资讯...</p>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error && articles.length === 0" class="error-container">
            <div class="error-icon">⚠️</div>
            <h3 class="error-title">获取资讯失败</h3>
            <p class="error-message">{{ error }}</p>
            <button class="retry-btn" @click="loadNews">重试</button>
        </div>

        <!-- 正常内容 -->
        <div v-else>
            <!-- 顶部操作栏 -->
            <div class="news-header-actions">
                <div class="news-info">
                    <span class="news-count">{{ articles.length }} 篇AI资讯</span>
                    <span v-if="lastUpdateTime" class="last-update">
                        最后更新: {{ formatUpdateTime(lastUpdateTime) }}
                    </span>
                </div>
                <button
                    class="refresh-btn"
                    @click="refreshNews"
                    :disabled="isRefreshing"
                    :class="{ refreshing: isRefreshing }"
                >
                    <span class="refresh-icon">🔄</span>
                    {{ isRefreshing ? '更新中...' : '刷新' }}
                </button>
            </div>

            <!-- 资讯列表 -->
            <div class="news-list">
                <div
                    v-for="article in articles"
                    :key="article.id"
                    class="news-card"
                    :class="{ featured: article.featured }"
                    @click="handleArticleClick(article)"
                >
                    <!-- 特色标签 -->
                    <div v-if="article.featured" class="featured-badge">精选</div>

                    <!-- 文章图片 -->
                    <div class="article-image">
                        <img :src="article.image" :alt="article.title" />
                    </div>

                    <!-- 文章内容 -->
                    <div class="article-content">
                        <div class="article-header">
                            <span class="article-category">{{ article.category }}</span>
                            <span class="article-source">{{ article.source }}</span>
                            <span class="article-date">{{ formatDate(article.date) }}</span>
                        </div>

                        <h3 class="article-title">{{ article.title }}</h3>

                        <div class="article-footer">
                            <div class="article-tags">
                                <span v-for="tag in article.tags" :key="tag" class="article-tag">
                                    {{ tag }}
                                </span>
                            </div>
                            <span class="read-count">{{ article.readCount }} 阅读</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 加载更多 -->
            <div class="load-more" v-if="articles.length > 0">
                <button class="load-more-btn" @click="loadMoreArticles" :disabled="isLoadingMore">
                    <span v-if="!isLoadingMore">加载更多</span>
                    <span v-else>加载中...</span>
                </button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import newsService from '@/services/newsService';

// 响应式状态
const articles = ref([]);
const isInitialLoading = ref(true);
const isRefreshing = ref(false);
const isLoadingMore = ref(false);
const error = ref('');
const lastUpdateTime = ref(null);

/**
 * 加载新闻数据
 * @param {boolean} forceRefresh - 是否强制刷新
 */
const loadNews = async (forceRefresh = false) => {
    try {
        error.value = '';

        if (forceRefresh) {
            isRefreshing.value = true;
        } else {
            isInitialLoading.value = true;
        }

        const newsData = await newsService.getAINews(forceRefresh);

        if (newsData && newsData.length > 0) {
            articles.value = newsData;
            lastUpdateTime.value = new Date();
            console.log(`成功加载 ${newsData.length} 条AI资讯`);
        } else {
            // 如果没有数据，显示提示
            if (articles.value.length === 0) {
                error.value = '暂无AI相关资讯，请稍后再试';
            }
        }
    } catch (err) {
        console.error('加载新闻失败:', err);
        error.value = err.message || '网络连接失败，请检查网络设置';
    } finally {
        isInitialLoading.value = false;
        isRefreshing.value = false;
    }
};

/**
 * 手动刷新新闻
 */
const refreshNews = () => {
    loadNews(true);
};

/**
 * 格式化日期
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串
 */
const formatDate = date => {
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
        return '昨天';
    } else if (diffDays <= 7) {
        return `${diffDays} 天前`;
    } else {
        return date.toLocaleDateString('zh-CN', {
            month: 'short',
            day: 'numeric'
        });
    }
};

/**
 * 格式化更新时间
 * @param {Date} date - 更新时间
 * @returns {string} 格式化后的时间字符串
 */
const formatUpdateTime = date => {
    return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
    });
};

/**
 * 处理文章点击事件
 * @param {object} article - 文章对象
 */
const handleArticleClick = article => {
    console.log('点击文章:', article.title);

    // 如果有URL，在新窗口打开
    if (article.url && article.url !== '#') {
        window.open(article.url, '_blank', 'noopener,noreferrer');
    } else {
        // 这里可以添加内部详情页跳转逻辑
        console.log('暂无外部链接，可添加详情页逻辑');
    }
};

/**
 * 加载更多文章
 */
const loadMoreArticles = async () => {
    try {
        isLoadingMore.value = true;

        // 刷新获取最新数据
        const newArticles = await newsService.refreshNews();
        if (newArticles && newArticles.length > articles.value.length) {
            articles.value = newArticles;
            console.log('加载更多文章成功');
        }
    } catch (err) {
        console.error('加载更多文章失败:', err);
    } finally {
        isLoadingMore.value = false;
    }
};

// 生命周期钩子
onMounted(() => {
    console.log('AI资讯页面已加载');
    loadNews();
});

onUnmounted(() => {
    // 清理资源，但不销毁全局服务（因为其他组件可能也在使用）
    console.log('AI资讯页面已卸载');
});
</script>

<style lang="scss" scoped>
.news-page {
    padding: 12px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

// 加载状态样式
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    color: var(--label-secondary, #666666);

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid var(--system-grouped-background-tertiary, #e5e5ea);
        border-top: 3px solid var(--system-blue, #007aff);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 16px;
    }

    .loading-text {
        font-size: 14px;
        margin: 0;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

// 错误状态样式
.error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    text-align: center;
    color: var(--label-secondary, #666666);

    .error-icon {
        font-size: 48px;
        margin-bottom: 16px;
    }

    .error-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--label-primary, #000000);
        margin: 0 0 8px 0;
    }

    .error-message {
        font-size: 14px;
        margin: 0 0 20px 0;
        max-width: 300px;
    }

    .retry-btn {
        background: var(--system-blue, #007aff);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 18px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
            background: #0056d0;
            transform: translateY(-1px);
        }
    }
}

// 顶部操作栏
.news-header-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    padding: 8px 0;

    .news-info {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .news-count {
            font-size: 16px;
            font-weight: 600;
            color: var(--label-primary, #000000);
        }

        .last-update {
            font-size: 12px;
            color: var(--label-tertiary, #999999);
        }
    }

    .refresh-btn {
        display: flex;
        align-items: center;
        gap: 6px;
        background: var(--system-blue, #007aff);
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 16px;
        font-size: 13px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover:not(:disabled) {
            background: #0056d0;
            transform: translateY(-1px);
        }

        &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        &.refreshing .refresh-icon {
            animation: spin 1s linear infinite;
        }

        .refresh-icon {
            transition: transform 0.3s ease;
        }
    }
}

.news-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 20px;
}

.news-card {
    display: flex;
    background-color: var(--system-grouped-background-secondary, #f2f2f7);
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border: 1px solid var(--separator-color-opaque, #e5e5e7);
    padding: 12px;

    &:hover {
        transform: translateY(-1px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
        border-color: var(--system-blue, #007aff);

        .article-title {
            color: var(--system-blue, #007aff);
        }
    }

    &.featured {
        border: 1.5px solid var(--system-blue, #007aff);
        box-shadow: 0 2px 12px rgba(0, 122, 255, 0.12);

        .featured-badge {
            position: absolute;
            top: 8px;
            left: 8px;
            background: linear-gradient(135deg, var(--system-blue, #007aff), #5856d6);
            color: white;
            padding: 2px 6px;
            border-radius: 6px;
            font-size: 10px;
            font-weight: 600;
            z-index: 2;
        }
    }

    .article-image {
        flex-shrink: 0;
        width: 100px;
        height: 80px;
        border-radius: 8px;
        overflow: hidden;
        margin-right: 12px;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
    }

    &:hover .article-image img {
        transform: scale(1.05);
    }

    .article-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        min-height: 80px;

        .article-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 6px;

            .article-category {
                background-color: var(--system-blue, #007aff);
                color: white;
                padding: 2px 6px;
                border-radius: 4px;
                font-size: 10px;
                font-weight: 600;
            }

            .article-source {
                background-color: var(--system-green, #34c759);
                color: white;
                padding: 2px 6px;
                border-radius: 4px;
                font-size: 10px;
                font-weight: 600;
            }

            .article-date {
                color: var(--label-tertiary, #999999);
                font-size: 10px;
                margin-left: auto;
            }
        }

        .article-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--label-primary, #000000);
            line-height: 1.3;
            transition: color 0.2s ease;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            margin-bottom: auto;
        }

        .article-footer {
            display: flex;
            align-items: flex-end;
            justify-content: space-between;
            margin-top: 8px;

            .article-tags {
                display: flex;
                gap: 4px;
                flex-wrap: wrap;
                flex: 1;

                .article-tag {
                    background-color: var(--system-grouped-background-tertiary, #e5e5ea);
                    color: var(--label-secondary, #666666);
                    padding: 1px 4px;
                    border-radius: 6px;
                    font-size: 9px;
                    font-weight: 500;
                }
            }

            .read-count {
                color: var(--label-tertiary, #999999);
                font-size: 10px;
                margin-left: 8px;
                flex-shrink: 0;
            }
        }
    }
}

.load-more {
    text-align: center;
    margin-top: 20px;

    .load-more-btn {
        background: linear-gradient(135deg, var(--system-blue, #007aff), #5856d6);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 18px;
        font-size: 13px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3);
        }

        &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
    }
}

// 响应式适配
@media (max-width: 480px) {
    .news-page {
        padding: 8px;
    }

    .news-header-actions {
        .news-info .news-count {
            font-size: 14px;
        }

        .refresh-btn {
            padding: 6px 12px;
            font-size: 12px;
        }
    }

    .news-card {
        padding: 10px;

        .article-image {
            width: 80px;
            height: 60px;
            margin-right: 10px;
        }

        .article-content {
            min-height: 60px;

            .article-title {
                font-size: 13px;
            }

            .article-header {
                .article-category,
                .article-source {
                    font-size: 9px;
                }
            }
        }
    }
}
</style>
